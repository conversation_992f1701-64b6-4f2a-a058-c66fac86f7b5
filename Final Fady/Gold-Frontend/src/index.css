@import "tailwindcss";
@import "./styles/animations.css";

/* Custom animations for hero slider */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelay {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-delay {
  animation: fadeInDelay 1s ease-out 0.3s both;
}

.animate-fade-in-delay-2 {
  animation: fadeInDelay 1s ease-out 0.6s both;
}

.animate-fade-in-delay-3 {
  animation: fadeInDelay 1s ease-out 0.9s both;
}

/* Additional animations for home page */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delay {
  animation: float 3s ease-in-out infinite 0.5s;
}

.animate-float-slow {
  animation: floatSlow 4s ease-in-out infinite;
}

.animate-float-delay-2 {
  animation: float 3s ease-in-out infinite 1s;
}

/* Product card improvements */
.product-card {
  min-height: 400px;
}

.product-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-card-actions {
  margin-top: auto;
}

/* Enhanced Shop Page Styles */
.shop-hero-gradient {
  background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%);
}

.shop-card-hover {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.shop-card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.shop-product-card {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.shop-product-card:hover {
  transform: translateY(-12px) rotateY(5deg);
  box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.3);
}

.shop-stats-icon {
  transition: all 0.3s ease;
}

.shop-stats-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom scrollbar for shop content */
.shop-content::-webkit-scrollbar {
  width: 6px;
}

.shop-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.shop-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.shop-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced shop page layout - Full Width */
.shop-container {
  width: 100%;
  max-width: none;
}

/* Optimized product cards for 4-column layout */
.product-card-optimized {
  min-height: 500px;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card-optimized:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Enhanced grid spacing for 4 columns */
.products-grid-4col {
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .products-grid-4col {
    gap: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .products-grid-4col {
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .products-grid-4col {
    gap: 2.25rem;
  }
}

/* Full width layout utilities */
.full-width-container {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

/* Compact card content */
.card-content-compact {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-content-compact .content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Shop List Page Enhancements */
.shop-card-enhanced {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.shop-card-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Filter Panel Sticky */
.filter-panel-sticky {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

/* Enhanced Grid Layout for Shops */
.shops-grid-enhanced {
  display: grid;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .shops-grid-enhanced {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .shops-grid-enhanced {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .shops-grid-enhanced {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Animation for shop cards */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
}

#root {
  min-height: 100vh;
}